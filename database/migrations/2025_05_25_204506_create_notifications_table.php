<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('type');
            $table->morphs('notifiable');
            $table->text('data');
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // Create owner-specific notifications table with enterprise isolation
        Schema::create('owner_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->enum('notification_type', [
                'booking', 'cancellation', 'payment', 'review', 'system',
                'marketing', 'alert', 'reminder', 'customer_message', 'waiting_list'
            ]);
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Event-specific data
            $table->string('source_type')->nullable(); // Source entity type
            $table->unsignedBigInteger('source_id')->nullable(); // Source entity ID
            $table->boolean('is_read')->default(false);
            $table->boolean('is_deleted')->default(false);
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->timestamps();

            // Indexes for performance and isolation
            $table->index(['business_id', 'owner_id']);
            $table->index(['notification_type', 'priority']);
            $table->index(['is_read', 'is_deleted']);
            $table->index(['created_at', 'priority']);
            $table->index(['source_type', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owner_notifications');
        Schema::dropIfExists('notifications');
    }
};
