@extends('owner.layouts.app')

@section('title', 'Visual Page Builder')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Visual Page Builder</h1>
            <p class="text-muted">Drag and drop to create your perfect landing page</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <button type="button" class="btn btn-outline-secondary" id="previewBtn">
                <i class="fas fa-eye"></i> Preview
            </button>
            <button type="button" class="btn btn-outline-info" id="saveBtn">
                <i class="fas fa-save"></i> Save
            </button>
            <button type="button" class="btn btn-success" id="publishBtn">
                <i class="fas fa-rocket"></i> Publish
            </button>
        </div>
    </div>
@stop

@section('content')
    <div class="row">
        <!-- Component Library -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-puzzle-piece mr-2"></i>
                        Components
                    </h3>
                </div>
                <div class="card-body p-0">
                    <div class="component-library">
                        <!-- Hero Section Components -->
                        <div class="component-category">
                            <div class="category-header" data-toggle="collapse" data-target="#heroComponents">
                                <i class="fas fa-star mr-2"></i>
                                Hero Sections
                                <i class="fas fa-chevron-down float-right"></i>
                            </div>
                            <div id="heroComponents" class="collapse show">
                                <div class="component-item" draggable="true" data-component="hero-basic">
                                    <div class="component-preview">
                                        <div class="mini-hero">
                                            <div class="mini-title"></div>
                                            <div class="mini-subtitle"></div>
                                            <div class="mini-button"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Basic Hero</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="hero-video">
                                    <div class="component-preview">
                                        <div class="mini-hero video">
                                            <i class="fas fa-play-circle"></i>
                                            <div class="mini-title"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Video Hero</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="hero-split">
                                    <div class="component-preview">
                                        <div class="mini-hero split">
                                            <div class="left">
                                                <div class="mini-title"></div>
                                                <div class="mini-subtitle"></div>
                                            </div>
                                            <div class="right"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Split Hero</div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Components -->
                        <div class="component-category">
                            <div class="category-header" data-toggle="collapse" data-target="#serviceComponents">
                                <i class="fas fa-cogs mr-2"></i>
                                Services
                                <i class="fas fa-chevron-down float-right"></i>
                            </div>
                            <div id="serviceComponents" class="collapse">
                                <div class="component-item" draggable="true" data-component="services-grid">
                                    <div class="component-preview">
                                        <div class="mini-grid">
                                            <div class="mini-card"></div>
                                            <div class="mini-card"></div>
                                            <div class="mini-card"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Service Grid</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="services-list">
                                    <div class="component-preview">
                                        <div class="mini-list">
                                            <div class="mini-list-item"></div>
                                            <div class="mini-list-item"></div>
                                            <div class="mini-list-item"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Service List</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="services-carousel">
                                    <div class="component-preview">
                                        <div class="mini-carousel">
                                            <div class="mini-slide"></div>
                                            <i class="fas fa-chevron-left"></i>
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </div>
                                    <div class="component-label">Service Carousel</div>
                                </div>
                            </div>
                        </div>

                        <!-- Content Components -->
                        <div class="component-category">
                            <div class="category-header" data-toggle="collapse" data-target="#contentComponents">
                                <i class="fas fa-align-left mr-2"></i>
                                Content
                                <i class="fas fa-chevron-down float-right"></i>
                            </div>
                            <div id="contentComponents" class="collapse">
                                <div class="component-item" draggable="true" data-component="about-section">
                                    <div class="component-preview">
                                        <div class="mini-about">
                                            <div class="mini-text"></div>
                                            <div class="mini-image"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">About Section</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="testimonials">
                                    <div class="component-preview">
                                        <div class="mini-testimonial">
                                            <div class="mini-quote"></div>
                                            <div class="mini-author"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Testimonials</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="team-section">
                                    <div class="component-preview">
                                        <div class="mini-team">
                                            <div class="mini-member"></div>
                                            <div class="mini-member"></div>
                                            <div class="mini-member"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Team Section</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="gallery">
                                    <div class="component-preview">
                                        <div class="mini-gallery">
                                            <div class="mini-photo"></div>
                                            <div class="mini-photo"></div>
                                            <div class="mini-photo"></div>
                                            <div class="mini-photo"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Gallery</div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Components -->
                        <div class="component-category">
                            <div class="category-header" data-toggle="collapse" data-target="#contactComponents">
                                <i class="fas fa-envelope mr-2"></i>
                                Contact
                                <i class="fas fa-chevron-down float-right"></i>
                            </div>
                            <div id="contactComponents" class="collapse">
                                <div class="component-item" draggable="true" data-component="contact-form">
                                    <div class="component-preview">
                                        <div class="mini-form">
                                            <div class="mini-input"></div>
                                            <div class="mini-input"></div>
                                            <div class="mini-textarea"></div>
                                            <div class="mini-button"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Contact Form</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="contact-info">
                                    <div class="component-preview">
                                        <div class="mini-contact">
                                            <div class="mini-info"></div>
                                            <div class="mini-info"></div>
                                            <div class="mini-info"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Contact Info</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="map">
                                    <div class="component-preview">
                                        <div class="mini-map">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                    </div>
                                    <div class="component-label">Map</div>
                                </div>
                            </div>
                        </div>

                        <!-- Booking Components -->
                        <div class="component-category">
                            <div class="category-header" data-toggle="collapse" data-target="#bookingComponents">
                                <i class="fas fa-calendar-check mr-2"></i>
                                Booking
                                <i class="fas fa-chevron-down float-right"></i>
                            </div>
                            <div id="bookingComponents" class="collapse">
                                <div class="component-item" draggable="true" data-component="booking-widget">
                                    <div class="component-preview">
                                        <div class="mini-booking">
                                            <div class="mini-calendar"></div>
                                            <div class="mini-button"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Booking Widget</div>
                                </div>
                                <div class="component-item" draggable="true" data-component="booking-cta">
                                    <div class="component-preview">
                                        <div class="mini-cta">
                                            <div class="mini-title"></div>
                                            <div class="mini-button large"></div>
                                        </div>
                                    </div>
                                    <div class="component-label">Booking CTA</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Builder Canvas -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-desktop mr-2"></i>
                        Page Canvas
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary device-preview active" data-device="desktop">
                                <i class="fas fa-desktop"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary device-preview" data-device="tablet">
                                <i class="fas fa-tablet-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary device-preview" data-device="mobile">
                                <i class="fas fa-mobile-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="page-canvas" id="pageCanvas">
                        <div class="canvas-container desktop-view">
                            <div class="drop-zone" id="dropZone">
                                <div class="empty-state">
                                    <i class="fas fa-plus-circle fa-3x text-muted"></i>
                                    <h4 class="text-muted mt-3">Start Building Your Page</h4>
                                    <p class="text-muted">Drag components from the left panel to build your landing page</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sliders-h mr-2"></i>
                        Properties
                    </h3>
                </div>
                <div class="card-body">
                    <div id="propertiesPanel">
                        <div class="no-selection">
                            <div class="text-center text-muted">
                                <i class="fas fa-mouse-pointer fa-2x"></i>
                                <p class="mt-2">Select a component to edit its properties</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
/* Visual Editor Styles */
.component-library {
    max-height: 600px;
    overflow-y: auto;
}

.component-category {
    border-bottom: 1px solid #dee2e6;
}

.category-header {
    padding: 12px 15px;
    background: #f8f9fa;
    cursor: pointer;
    font-weight: 500;
    border-bottom: 1px solid #dee2e6;
}

.category-header:hover {
    background: #e9ecef;
}

.component-item {
    padding: 10px 15px;
    cursor: grab;
    border-bottom: 1px solid #f1f1f1;
    transition: all 0.2s ease;
}

.component-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.component-item:active {
    cursor: grabbing;
}

.component-preview {
    width: 100%;
    height: 40px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 5px;
    position: relative;
    overflow: hidden;
}

.component-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

/* Mini component previews */
.mini-hero {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.mini-hero.video {
    background: #000;
    color: white;
}

.mini-hero.split {
    flex-direction: row;
}

.mini-hero .left {
    flex: 1;
    padding: 5px;
}

.mini-hero .right {
    flex: 1;
    background: rgba(255,255,255,0.2);
}

.mini-title {
    width: 60%;
    height: 3px;
    background: rgba(255,255,255,0.8);
    margin: 2px auto;
    border-radius: 1px;
}

.mini-subtitle {
    width: 40%;
    height: 2px;
    background: rgba(255,255,255,0.6);
    margin: 2px auto;
    border-radius: 1px;
}

.mini-button {
    width: 30%;
    height: 6px;
    background: #28a745;
    margin: 3px auto;
    border-radius: 2px;
}

.mini-button.large {
    width: 50%;
    height: 8px;
}

.mini-grid {
    display: flex;
    gap: 2px;
    padding: 5px;
    height: 100%;
}

.mini-card {
    flex: 1;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 2px;
}

.mini-list {
    padding: 5px;
    height: 100%;
}

.mini-list-item {
    height: 8px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-bottom: 2px;
    border-radius: 1px;
}

.mini-carousel {
    position: relative;
    height: 100%;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.mini-slide {
    width: 80%;
    height: 80%;
    background: #dee2e6;
    margin: 10% auto;
    border-radius: 2px;
}

.mini-carousel i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
    color: #6c757d;
}

.mini-carousel .fa-chevron-left {
    left: 2px;
}

.mini-carousel .fa-chevron-right {
    right: 2px;
}

.mini-about {
    display: flex;
    padding: 5px;
    height: 100%;
}

.mini-text {
    flex: 2;
    background: #f8f9fa;
    margin-right: 2px;
    border-radius: 1px;
}

.mini-image {
    flex: 1;
    background: #dee2e6;
    border-radius: 1px;
}

.mini-testimonial {
    padding: 5px;
    height: 100%;
    text-align: center;
}

.mini-quote {
    width: 80%;
    height: 15px;
    background: #f8f9fa;
    margin: 5px auto;
    border-radius: 1px;
}

.mini-author {
    width: 40%;
    height: 8px;
    background: #dee2e6;
    margin: 0 auto;
    border-radius: 1px;
}

.mini-team {
    display: flex;
    gap: 2px;
    padding: 5px;
    height: 100%;
}

.mini-member {
    flex: 1;
    background: #f8f9fa;
    border-radius: 50%;
}

.mini-gallery {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    padding: 5px;
    height: 100%;
}

.mini-photo {
    background: #dee2e6;
    border-radius: 1px;
}

.mini-form {
    padding: 5px;
    height: 100%;
}

.mini-input {
    width: 100%;
    height: 6px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-bottom: 2px;
    border-radius: 1px;
}

.mini-textarea {
    width: 100%;
    height: 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-bottom: 2px;
    border-radius: 1px;
}

.mini-contact {
    padding: 5px;
    height: 100%;
}

.mini-info {
    width: 80%;
    height: 8px;
    background: #f8f9fa;
    margin-bottom: 2px;
    border-radius: 1px;
}

.mini-map {
    height: 100%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.mini-booking {
    padding: 5px;
    height: 100%;
}

.mini-calendar {
    width: 100%;
    height: 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-bottom: 3px;
    border-radius: 1px;
}

.mini-cta {
    padding: 5px;
    height: 100%;
    text-align: center;
}

/* Page Canvas */
.page-canvas {
    min-height: 600px;
    background: #f8f9fa;
}

.canvas-container {
    width: 100%;
    transition: all 0.3s ease;
}

.canvas-container.desktop-view {
    width: 100%;
}

.canvas-container.tablet-view {
    width: 768px;
    margin: 0 auto;
}

.canvas-container.mobile-view {
    width: 375px;
    margin: 0 auto;
}

.drop-zone {
    min-height: 600px;
    background: white;
    border: 2px dashed #dee2e6;
    position: relative;
}

.drop-zone.drag-over {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
}

.empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.page-section {
    position: relative;
    border: 1px solid transparent;
    margin: 10px 0;
    transition: all 0.2s ease;
}

.page-section:hover {
    border-color: #007bff;
}

.page-section.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.section-controls {
    position: absolute;
    top: -30px;
    right: 0;
    background: #007bff;
    border-radius: 4px;
    padding: 2px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.page-section:hover .section-controls,
.page-section.selected .section-controls {
    opacity: 1;
}

.section-controls button {
    background: none;
    border: none;
    color: white;
    padding: 4px 6px;
    font-size: 12px;
    cursor: pointer;
}

.section-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Device Preview Buttons */
.device-preview.active {
    background: #007bff;
    color: white;
}

/* Properties Panel */
.no-selection {
    padding: 40px 20px;
}

.property-group {
    margin-bottom: 20px;
}

.property-group h6 {
    font-weight: 600;
    margin-bottom: 10px;
    color: #495057;
}

.property-input {
    margin-bottom: 10px;
}

.color-picker {
    width: 40px;
    height: 30px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .canvas-container.tablet-view,
    .canvas-container.mobile-view {
        width: 100%;
    }
}
</style>
@endsection

@section('js')
<script>
$(document).ready(function() {
    console.log('Visual Page Builder initialized');

    let selectedSection = null;
    let sectionCounter = 0;
    let pageData = {
        sections: [],
        settings: {
            theme: 'modern',
            device: 'desktop'
        }
    };

    // Initialize drag and drop
    initializeDragAndDrop();

    // Initialize device preview
    initializeDevicePreview();

    // Initialize toolbar actions
    initializeToolbar();

    // Drag and Drop functionality
    function initializeDragAndDrop() {
        // Make components draggable
        $('.component-item').each(function() {
            this.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', $(this).data('component'));
                $(this).addClass('dragging');
            });

            this.addEventListener('dragend', function(e) {
                $(this).removeClass('dragging');
            });
        });

        // Set up drop zone
        const dropZone = document.getElementById('dropZone');

        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });

        dropZone.addEventListener('dragleave', function(e) {
            if (!dropZone.contains(e.relatedTarget)) {
                $(this).removeClass('drag-over');
            }
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');

            const componentType = e.dataTransfer.getData('text/plain');
            addComponentToPage(componentType);
        });
    }

    // Add component to page
    function addComponentToPage(componentType) {
        sectionCounter++;
        const sectionId = 'section-' + sectionCounter;

        // Hide empty state if this is the first component
        if ($('.page-section').length === 0) {
            $('.empty-state').hide();
        }

        // Create section HTML based on component type
        const sectionHtml = generateSectionHtml(componentType, sectionId);

        // Add to drop zone
        $('#dropZone').append(sectionHtml);

        // Add to page data
        pageData.sections.push({
            id: sectionId,
            type: componentType,
            content: getDefaultContent(componentType),
            styles: getDefaultStyles(componentType)
        });

        // Select the new section
        selectSection(sectionId);

        console.log('Added component:', componentType);
    }

    // Generate section HTML
    function generateSectionHtml(componentType, sectionId) {
        const content = getComponentHtml(componentType);

        return `
            <div class="page-section" id="${sectionId}" data-type="${componentType}">
                <div class="section-controls">
                    <button type="button" class="edit-section" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="move-up" title="Move Up">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button type="button" class="move-down" title="Move Down">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button type="button" class="delete-section" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="section-content">
                    ${content}
                </div>
            </div>
        `;
    }

    // Get component HTML based on type
    function getComponentHtml(componentType) {
        const templates = {
            'hero-basic': `
                <div class="hero-section bg-primary text-white py-5">
                    <div class="container text-center">
                        <h1 class="display-4 mb-4">Welcome to Our Business</h1>
                        <p class="lead mb-4">We provide exceptional services to help you succeed</p>
                        <a href="#" class="btn btn-success btn-lg">Book Now</a>
                    </div>
                </div>
            `,
            'hero-video': `
                <div class="hero-section bg-dark text-white py-5 position-relative">
                    <div class="container text-center">
                        <div class="video-placeholder bg-secondary mb-4" style="height: 200px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-play-circle fa-4x"></i>
                        </div>
                        <h1 class="display-4 mb-4">Experience Our Services</h1>
                        <a href="#" class="btn btn-primary btn-lg">Watch Video</a>
                    </div>
                </div>
            `,
            'hero-split': `
                <div class="hero-section py-5">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h1 class="display-4 mb-4">Professional Services</h1>
                                <p class="lead mb-4">Transform your business with our expert solutions</p>
                                <a href="#" class="btn btn-primary btn-lg">Get Started</a>
                            </div>
                            <div class="col-md-6">
                                <div class="image-placeholder bg-light" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'services-grid': `
                <div class="services-section py-5">
                    <div class="container">
                        <div class="text-center mb-5">
                            <h2>Our Services</h2>
                            <p class="lead">Discover what we can do for you</p>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Service One</h5>
                                        <p class="card-text">Description of your first service</p>
                                        <a href="#" class="btn btn-primary">Learn More</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-star fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Service Two</h5>
                                        <p class="card-text">Description of your second service</p>
                                        <a href="#" class="btn btn-primary">Learn More</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-heart fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Service Three</h5>
                                        <p class="card-text">Description of your third service</p>
                                        <a href="#" class="btn btn-primary">Learn More</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'about-section': `
                <div class="about-section py-5">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h2 class="mb-4">About Our Business</h2>
                                <p class="mb-4">We are dedicated to providing exceptional services that exceed our customers' expectations. With years of experience and a commitment to excellence, we have built a reputation for quality and reliability.</p>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success mr-2"></i> Professional Service</li>
                                    <li><i class="fas fa-check text-success mr-2"></i> Experienced Team</li>
                                    <li><i class="fas fa-check text-success mr-2"></i> Customer Satisfaction</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <div class="image-placeholder bg-light" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'contact-form': `
                <div class="contact-section py-5 bg-light">
                    <div class="container">
                        <div class="text-center mb-5">
                            <h2>Contact Us</h2>
                            <p class="lead">Get in touch with our team</p>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <form>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="name">Name</label>
                                                <input type="text" class="form-control" id="name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="email">Email</label>
                                                <input type="email" class="form-control" id="email" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="subject">Subject</label>
                                        <input type="text" class="form-control" id="subject">
                                    </div>
                                    <div class="form-group">
                                        <label for="message">Message</label>
                                        <textarea class="form-control" id="message" rows="5" required></textarea>
                                    </div>
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'booking-widget': `
                <div class="booking-section py-5">
                    <div class="container">
                        <div class="text-center mb-5">
                            <h2>Book an Appointment</h2>
                            <p class="lead">Schedule your visit with us</p>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="booking-widget-placeholder bg-light p-5 text-center">
                                            <i class="fas fa-calendar-alt fa-3x text-primary mb-3"></i>
                                            <h5>Booking Calendar</h5>
                                            <p class="text-muted">Interactive booking calendar will be displayed here</p>
                                            <button class="btn btn-primary">Book Now</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        return templates[componentType] || '<div class="p-4 text-center">Component not found</div>';
    }

    // Get default content for component
    function getDefaultContent(componentType) {
        const defaults = {
            'hero-basic': {
                title: 'Welcome to Our Business',
                subtitle: 'We provide exceptional services to help you succeed',
                buttonText: 'Book Now',
                buttonLink: '#'
            },
            'hero-video': {
                title: 'Experience Our Services',
                videoUrl: '',
                buttonText: 'Watch Video',
                buttonLink: '#'
            },
            'services-grid': {
                title: 'Our Services',
                subtitle: 'Discover what we can do for you',
                services: [
                    { title: 'Service One', description: 'Description of your first service', icon: 'fas fa-cog' },
                    { title: 'Service Two', description: 'Description of your second service', icon: 'fas fa-star' },
                    { title: 'Service Three', description: 'Description of your third service', icon: 'fas fa-heart' }
                ]
            },
            'about-section': {
                title: 'About Our Business',
                content: 'We are dedicated to providing exceptional services...',
                features: ['Professional Service', 'Experienced Team', 'Customer Satisfaction'],
                imageUrl: ''
            },
            'contact-form': {
                title: 'Contact Us',
                subtitle: 'Get in touch with our team',
                fields: ['name', 'email', 'subject', 'message']
            },
            'booking-widget': {
                title: 'Book an Appointment',
                subtitle: 'Schedule your visit with us',
                buttonText: 'Book Now'
            }
        };

        return defaults[componentType] || {};
    }

    // Get default styles for component
    function getDefaultStyles(componentType) {
        return {
            backgroundColor: '',
            textColor: '',
            padding: '5rem 0',
            margin: '0'
        };
    }

    // Section selection and interaction
    $(document).on('click', '.page-section', function(e) {
        e.stopPropagation();
        selectSection($(this).attr('id'));
    });

    // Section controls
    $(document).on('click', '.edit-section', function(e) {
        e.stopPropagation();
        const sectionId = $(this).closest('.page-section').attr('id');
        editSection(sectionId);
    });

    $(document).on('click', '.delete-section', function(e) {
        e.stopPropagation();
        const sectionId = $(this).closest('.page-section').attr('id');
        deleteSection(sectionId);
    });

    $(document).on('click', '.move-up', function(e) {
        e.stopPropagation();
        const section = $(this).closest('.page-section');
        const prev = section.prev('.page-section');
        if (prev.length) {
            section.insertBefore(prev);
        }
    });

    $(document).on('click', '.move-down', function(e) {
        e.stopPropagation();
        const section = $(this).closest('.page-section');
        const next = section.next('.page-section');
        if (next.length) {
            section.insertAfter(next);
        }
    });

    // Select section
    function selectSection(sectionId) {
        $('.page-section').removeClass('selected');
        $('#' + sectionId).addClass('selected');
        selectedSection = sectionId;

        // Update properties panel
        updatePropertiesPanel(sectionId);
    }

    // Update properties panel
    function updatePropertiesPanel(sectionId) {
        const section = $('#' + sectionId);
        const sectionType = section.data('type');
        const sectionData = pageData.sections.find(s => s.id === sectionId);

        let propertiesHtml = `
            <div class="property-group">
                <h6>Section Properties</h6>
                <div class="property-input">
                    <label>Section Type</label>
                    <input type="text" class="form-control" value="${sectionType}" readonly>
                </div>
            </div>
        `;

        // Add type-specific properties
        if (sectionType.includes('hero')) {
            propertiesHtml += `
                <div class="property-group">
                    <h6>Content</h6>
                    <div class="property-input">
                        <label>Title</label>
                        <input type="text" class="form-control section-property" data-property="title" value="${sectionData.content.title || ''}">
                    </div>
                    <div class="property-input">
                        <label>Subtitle</label>
                        <textarea class="form-control section-property" data-property="subtitle" rows="2">${sectionData.content.subtitle || ''}</textarea>
                    </div>
                    <div class="property-input">
                        <label>Button Text</label>
                        <input type="text" class="form-control section-property" data-property="buttonText" value="${sectionData.content.buttonText || ''}">
                    </div>
                </div>
            `;
        }

        propertiesHtml += `
            <div class="property-group">
                <h6>Styling</h6>
                <div class="property-input">
                    <label>Background Color</label>
                    <input type="color" class="form-control color-picker section-style" data-style="backgroundColor" value="${sectionData.styles.backgroundColor || '#ffffff'}">
                </div>
                <div class="property-input">
                    <label>Text Color</label>
                    <input type="color" class="form-control color-picker section-style" data-style="textColor" value="${sectionData.styles.textColor || '#000000'}">
                </div>
            </div>
            <div class="property-group">
                <h6>Actions</h6>
                <button type="button" class="btn btn-danger btn-sm btn-block" onclick="deleteSection('${sectionId}')">
                    <i class="fas fa-trash mr-1"></i> Delete Section
                </button>
            </div>
        `;

        $('#propertiesPanel').html(propertiesHtml);
    }

    // Handle property changes
    $(document).on('input', '.section-property', function() {
        const property = $(this).data('property');
        const value = $(this).val();
        const sectionData = pageData.sections.find(s => s.id === selectedSection);

        if (sectionData) {
            sectionData.content[property] = value;
            updateSectionContent(selectedSection);
        }
    });

    $(document).on('input', '.section-style', function() {
        const style = $(this).data('style');
        const value = $(this).val();
        const sectionData = pageData.sections.find(s => s.id === selectedSection);

        if (sectionData) {
            sectionData.styles[style] = value;
            updateSectionStyles(selectedSection);
        }
    });

    // Update section content
    function updateSectionContent(sectionId) {
        const section = $('#' + sectionId);
        const sectionData = pageData.sections.find(s => s.id === sectionId);

        // Update title
        if (sectionData.content.title) {
            section.find('h1, h2').first().text(sectionData.content.title);
        }

        // Update subtitle
        if (sectionData.content.subtitle) {
            section.find('p.lead, .lead').first().text(sectionData.content.subtitle);
        }

        // Update button text
        if (sectionData.content.buttonText) {
            section.find('.btn').first().text(sectionData.content.buttonText);
        }
    }

    // Update section styles
    function updateSectionStyles(sectionId) {
        const section = $('#' + sectionId);
        const sectionData = pageData.sections.find(s => s.id === sectionId);
        const styles = sectionData.styles;

        const sectionContent = section.find('.section-content > div').first();

        if (styles.backgroundColor) {
            sectionContent.css('background-color', styles.backgroundColor);
        }

        if (styles.textColor) {
            sectionContent.css('color', styles.textColor);
        }
    }

    // Delete section
    function deleteSection(sectionId) {
        if (confirm('Are you sure you want to delete this section?')) {
            $('#' + sectionId).remove();
            pageData.sections = pageData.sections.filter(s => s.id !== sectionId);

            // Show empty state if no sections left
            if ($('.page-section').length === 0) {
                $('.empty-state').show();
            }

            // Clear properties panel
            $('#propertiesPanel').html(`
                <div class="no-selection">
                    <div class="text-center text-muted">
                        <i class="fas fa-mouse-pointer fa-2x"></i>
                        <p class="mt-2">Select a component to edit its properties</p>
                    </div>
                </div>
            `);

            selectedSection = null;
        }
    }

    // Device preview functionality
    function initializeDevicePreview() {
        $('.device-preview').click(function() {
            $('.device-preview').removeClass('active');
            $(this).addClass('active');

            const device = $(this).data('device');
            const canvas = $('.canvas-container');

            canvas.removeClass('desktop-view tablet-view mobile-view');
            canvas.addClass(device + '-view');

            pageData.settings.device = device;
        });
    }

    // Toolbar functionality
    function initializeToolbar() {
        $('#previewBtn').click(function() {
            // Open preview in new window
            const previewData = JSON.stringify(pageData);
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(generatePreviewHtml());
        });

        $('#saveBtn').click(function() {
            savePage();
        });

        $('#publishBtn').click(function() {
            publishPage();
        });
    }

    // Generate preview HTML
    function generatePreviewHtml() {
        let html = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Landing Page Preview</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            </head>
            <body>
        `;

        $('.page-section').each(function() {
            html += $(this).find('.section-content').html();
        });

        html += `
            </body>
            </html>
        `;

        return html;
    }

    // Save page
    function savePage() {
        const saveData = {
            sections: pageData.sections,
            settings: pageData.settings,
            html: generatePreviewHtml()
        };

        // Here you would send the data to your backend
        console.log('Saving page data:', saveData);

        // Simulate save
        setTimeout(() => {
            toastr.success('Page saved successfully!');
        }, 500);
    }

    // Publish page
    function publishPage() {
        if (confirm('Are you sure you want to publish this page? It will be visible to visitors.')) {
            savePage();

            // Simulate publish
            setTimeout(() => {
                toastr.success('Page published successfully!');
            }, 1000);
        }
    }

    // Clear canvas when clicking outside sections
    $(document).on('click', '#dropZone', function(e) {
        if (e.target === this) {
            $('.page-section').removeClass('selected');
            selectedSection = null;

            $('#propertiesPanel').html(`
                <div class="no-selection">
                    <div class="text-center text-muted">
                        <i class="fas fa-mouse-pointer fa-2x"></i>
                        <p class="mt-2">Select a component to edit its properties</p>
                    </div>
                </div>
            `);
        }
    });
});
</script>
@endsection