<?php

namespace App\Events;

use App\Models\OwnerNotification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OwnerNotificationUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $notification;
    public $action;

    /**
     * Create a new event instance.
     */
    public function __construct(OwnerNotification $notification, string $action = 'updated')
    {
        $this->notification = $notification;
        $this->action = $action;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('owner-notifications.' . $this->notification->owner_id)
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'notification.updated';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->notification->id,
            'type' => 'notification_updated',
            'action' => $this->action,
            'notification' => [
                'id' => $this->notification->id,
                'business_id' => $this->notification->business_id,
                'notification_type' => $this->notification->notification_type,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'data' => $this->notification->data,
                'priority' => $this->notification->priority,
                'is_read' => $this->notification->is_read,
                'is_deleted' => $this->notification->is_deleted,
                'read_at' => $this->notification->read_at?->toISOString(),
                'deleted_at' => $this->notification->deleted_at?->toISOString(),
                'created_at' => $this->notification->created_at->toISOString(),
                'formatted_time' => $this->notification->created_at->diffForHumans(),
            ]
        ];
    }
}
