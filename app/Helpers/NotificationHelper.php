<?php

namespace App\Helpers;

use App\Jobs\ProcessOwnerNotification;
use App\Models\Business;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationHelper
{
    /**
     * Dispatch a notification for a booking event.
     */
    public static function notifyBookingEvent(Booking $booking, string $eventType)
    {
        try {
            $business = $booking->business;
            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send notification: Business or owner not found', [
                    'booking_id' => $booking->id,
                    'business_id' => $booking->business_id
                ]);
                return false;
            }

            $notificationData = [
                'booking_id' => $booking->id,
                'customer_name' => $booking->customer_name,
                'customer_email' => $booking->customer_email,
                'customer_phone' => $booking->customer_phone,
                'service_name' => $booking->service->name ?? 'Unknown Service',
                'service_id' => $booking->service_id,
                'appointment_date' => $booking->scheduled_at?->toISOString(),
                'booking_value' => $booking->total_amount,
                'status' => $booking->status,
                'notes' => $booking->notes,
            ];

            ProcessOwnerNotification::dispatch(
                $eventType,
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch booking notification', [
                'booking_id' => $booking->id,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a notification for a payment event.
     */
    public static function notifyPaymentEvent($payment, string $eventType, $business = null)
    {
        try {
            if (!$business) {
                $business = $payment->business ?? $payment->booking->business ?? null;
            }

            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send payment notification: Business or owner not found', [
                    'payment_id' => $payment->id ?? 'unknown'
                ]);
                return false;
            }

            $notificationData = [
                'payment_id' => $payment->id ?? null,
                'amount' => $payment->amount ?? 0,
                'currency' => $payment->currency ?? 'USD',
                'customer_name' => $payment->customer_name ?? $payment->booking->customer_name ?? 'Unknown Customer',
                'booking_id' => $payment->booking_id ?? null,
                'transaction_id' => $payment->transaction_id ?? null,
                'payment_method' => $payment->payment_method ?? null,
                'status' => $payment->status ?? null,
            ];

            if ($eventType === 'payment_failed') {
                $notificationData['failure_reason'] = $payment->failure_reason ?? 'Unknown error';
            }

            ProcessOwnerNotification::dispatch(
                $eventType,
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch payment notification', [
                'payment_id' => $payment->id ?? 'unknown',
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a notification for a review event.
     */
    public static function notifyReviewEvent($review, $business = null)
    {
        try {
            if (!$business) {
                $business = $review->business ?? $review->booking->business ?? null;
            }

            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send review notification: Business or owner not found', [
                    'review_id' => $review->id ?? 'unknown'
                ]);
                return false;
            }

            $notificationData = [
                'review_id' => $review->id,
                'rating' => $review->rating,
                'customer_name' => $review->customer_name ?? $review->user->name ?? 'Anonymous',
                'service_name' => $review->service->name ?? 'Unknown Service',
                'comment' => $review->comment ?? '',
                'booking_id' => $review->booking_id ?? null,
            ];

            ProcessOwnerNotification::dispatch(
                'review_submitted',
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch review notification', [
                'review_id' => $review->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a notification for a waiting list event.
     */
    public static function notifyWaitingListEvent($waitingList, string $eventType = 'waiting_list_added')
    {
        try {
            $business = $waitingList->business;
            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send waiting list notification: Business or owner not found', [
                    'waiting_list_id' => $waitingList->id
                ]);
                return false;
            }

            $notificationData = [
                'waiting_list_id' => $waitingList->id,
                'customer_name' => $waitingList->customer_name,
                'customer_email' => $waitingList->customer_email,
                'customer_phone' => $waitingList->customer_phone,
                'service_name' => $waitingList->service->name ?? 'Unknown Service',
                'service_id' => $waitingList->service_id,
                'preferred_date' => $waitingList->preferred_date?->toDateString(),
                'status' => $waitingList->status,
                'priority' => $waitingList->priority,
            ];

            ProcessOwnerNotification::dispatch(
                $eventType,
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch waiting list notification', [
                'waiting_list_id' => $waitingList->id,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a notification for a customer message.
     */
    public static function notifyCustomerMessage($message, $business = null)
    {
        try {
            if (!$business) {
                $business = $message->business ?? null;
            }

            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send customer message notification: Business or owner not found', [
                    'message_id' => $message->id ?? 'unknown'
                ]);
                return false;
            }

            $notificationData = [
                'message_id' => $message->id,
                'customer_name' => $message->customer_name ?? $message->user->name ?? 'Unknown Customer',
                'customer_email' => $message->customer_email ?? $message->user->email ?? null,
                'subject' => $message->subject ?? 'No Subject',
                'message' => $message->message ?? '',
                'type' => $message->type ?? 'general',
            ];

            ProcessOwnerNotification::dispatch(
                'customer_message',
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch customer message notification', [
                'message_id' => $message->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a system alert notification.
     */
    public static function notifySystemAlert($businessId, $title, $message, $priority = 'high', $data = [])
    {
        try {
            $business = Business::find($businessId);
            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send system alert: Business or owner not found', [
                    'business_id' => $businessId
                ]);
                return false;
            }

            $notificationData = array_merge([
                'title' => $title,
                'message' => $message,
                'priority' => $priority,
                'alert_type' => 'system',
            ], $data);

            ProcessOwnerNotification::dispatch(
                'system_alert',
                $notificationData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch system alert notification', [
                'business_id' => $businessId,
                'title' => $title,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch a marketing campaign notification.
     */
    public static function notifyMarketingCampaign($businessId, $campaignData)
    {
        try {
            $business = Business::find($businessId);
            if (!$business || !$business->owner_id) {
                Log::warning('Cannot send marketing notification: Business or owner not found', [
                    'business_id' => $businessId
                ]);
                return false;
            }

            ProcessOwnerNotification::dispatch(
                'marketing_campaign',
                $campaignData,
                $business->id,
                $business->owner_id
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch marketing notification', [
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Dispatch notifications to all business owners.
     */
    public static function notifyAllBusinesses($title, $message, $priority = 'normal', $data = [])
    {
        try {
            $businesses = Business::with('owner')->active()->get();
            $count = 0;

            foreach ($businesses as $business) {
                if ($business->owner_id) {
                    $notificationData = array_merge([
                        'title' => $title,
                        'message' => $message,
                        'priority' => $priority,
                        'broadcast' => true,
                    ], $data);

                    ProcessOwnerNotification::dispatch(
                        'system_alert',
                        $notificationData,
                        $business->id,
                        $business->owner_id
                    );
                    $count++;
                }
            }

            Log::info('Broadcast notification sent to all businesses', [
                'title' => $title,
                'business_count' => $count
            ]);

            return $count;
        } catch (\Exception $e) {
            Log::error('Failed to dispatch broadcast notification', [
                'title' => $title,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
}
