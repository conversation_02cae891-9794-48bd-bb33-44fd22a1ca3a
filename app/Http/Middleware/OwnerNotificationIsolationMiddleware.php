<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Auth;

class OwnerNotificationIsolationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to authenticated users with owner role
        if (!Auth::check() || !Auth::user()->hasRole('owner')) {
            return $next($request);
        }

        // Get the authenticated user's business
        $user = Auth::user();
        $business = $user->ownedBusinesses()->active()->first();

        if (!$business) {
            // If user doesn't have a business, they shouldn't access notifications
            abort(403, 'You must have an active business to access notifications.');
        }

        // Add global scope to ensure all notification queries are scoped to the owner's business
        OwnerNotification::addGlobalScope('owner_business_isolation', function ($query) use ($business, $user) {
            $query->where('business_id', $business->id)
                  ->where('owner_id', $user->id);
        });

        // Store business and owner info in request for controllers
        $request->merge([
            '_owner_business_id' => $business->id,
            '_owner_id' => $user->id
        ]);

        return $next($request);
    }
}
