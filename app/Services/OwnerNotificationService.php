<?php

namespace App\Services;

use App\Models\OwnerNotification;
use App\Models\Booking;
use App\Models\Business;
use App\Models\User;
use App\Models\WaitingList;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OwnerNotificationService
{
    /**
     * Create a notification for a business owner.
     */
    public function createNotification(
        $businessId,
        $ownerId,
        $type,
        $title,
        $message,
        $data = [],
        $priority = 'normal',
        $sourceType = null,
        $sourceId = null,
        $expiresAt = null
    ) {
        try {
            return OwnerNotification::create([
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'notification_type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'source_type' => $sourceType,
                'source_id' => $sourceId,
                'priority' => $priority,
                'expires_at' => $expiresAt,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create owner notification', [
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Create booking-related notification.
     */
    public function createBookingNotification(Booking $booking, $type = 'booking')
    {
        $business = $booking->business;
        
        $titles = [
            'booking' => 'New Booking Received',
            'cancellation' => 'Booking Cancelled',
            'modification' => 'Booking Modified',
            'reminder' => 'Booking Reminder',
            'no_show' => 'Customer No-Show',
            'check_in' => 'Customer Checked In',
            'check_out' => 'Customer Checked Out',
        ];

        $messages = [
            'booking' => "New appointment booked by {$booking->customer_name} for {$booking->service->name} on {$booking->scheduled_at->format('M j, Y \a\t g:i A')}",
            'cancellation' => "Appointment cancelled by {$booking->customer_name} for {$booking->service->name} scheduled on {$booking->scheduled_at->format('M j, Y \a\t g:i A')}",
            'modification' => "Appointment modified by {$booking->customer_name} for {$booking->service->name}",
            'reminder' => "Upcoming appointment with {$booking->customer_name} for {$booking->service->name} on {$booking->scheduled_at->format('M j, Y \a\t g:i A')}",
            'no_show' => "Customer {$booking->customer_name} did not show up for {$booking->service->name} appointment",
            'check_in' => "Customer {$booking->customer_name} has checked in for {$booking->service->name}",
            'check_out' => "Customer {$booking->customer_name} has checked out from {$booking->service->name}",
        ];

        $priorities = [
            'booking' => 'normal',
            'cancellation' => 'high',
            'modification' => 'normal',
            'reminder' => 'normal',
            'no_show' => 'high',
            'check_in' => 'normal',
            'check_out' => 'normal',
        ];

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            $type,
            $titles[$type] ?? 'Booking Update',
            $messages[$type] ?? 'Booking status updated',
            [
                'booking_id' => $booking->id,
                'customer_name' => $booking->customer_name,
                'customer_email' => $booking->customer_email,
                'customer_phone' => $booking->customer_phone,
                'service_name' => $booking->service->name ?? 'Unknown Service',
                'service_id' => $booking->service_id,
                'appointment_date' => $booking->scheduled_at?->toISOString(),
                'booking_value' => $booking->total_amount,
                'status' => $booking->status,
            ],
            $priorities[$type] ?? 'normal',
            'booking',
            $booking->id
        );
    }

    /**
     * Create payment-related notification.
     */
    public function createPaymentNotification($business, $type, $data)
    {
        $titles = [
            'payment_received' => 'Payment Received',
            'payment_failed' => 'Payment Failed',
            'refund_processed' => 'Refund Processed',
            'payout_completed' => 'Payout Completed',
        ];

        $priorities = [
            'payment_received' => 'normal',
            'payment_failed' => 'high',
            'refund_processed' => 'normal',
            'payout_completed' => 'normal',
        ];

        $amount = $data['amount'] ?? 0;
        $currency = $data['currency'] ?? 'USD';
        $customerName = $data['customer_name'] ?? 'Unknown Customer';

        $messages = [
            'payment_received' => "Payment of {$currency} {$amount} received from {$customerName}",
            'payment_failed' => "Payment of {$currency} {$amount} failed for {$customerName}",
            'refund_processed' => "Refund of {$currency} {$amount} processed for {$customerName}",
            'payout_completed' => "Payout of {$currency} {$amount} has been completed",
        ];

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'payment',
            $titles[$type] ?? 'Payment Update',
            $messages[$type] ?? 'Payment status updated',
            $data,
            $priorities[$type] ?? 'normal',
            'payment',
            $data['payment_id'] ?? null
        );
    }

    /**
     * Create customer review notification.
     */
    public function createReviewNotification($business, $review)
    {
        $rating = $review['rating'] ?? 0;
        $customerName = $review['customer_name'] ?? 'Anonymous';
        $serviceName = $review['service_name'] ?? 'Unknown Service';

        $title = $rating >= 4 ? 'New Positive Review' : ($rating <= 2 ? 'New Negative Review' : 'New Review');
        $priority = $rating <= 2 ? 'high' : 'normal';

        $stars = str_repeat('⭐', $rating);
        $message = "New {$rating}-star review from {$customerName} for {$serviceName}: {$stars}";

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'review',
            $title,
            $message,
            $review,
            $priority,
            'review',
            $review['review_id'] ?? null
        );
    }

    /**
     * Create waiting list notification.
     */
    public function createWaitingListNotification(WaitingList $waitingList, $type = 'new_entry')
    {
        $business = $waitingList->business;

        $titles = [
            'new_entry' => 'New Waiting List Entry',
            'slot_available' => 'Slot Available for Waiting Customer',
            'customer_notified' => 'Customer Notified',
            'converted_to_booking' => 'Waiting List Converted to Booking',
        ];

        $messages = [
            'new_entry' => "New customer {$waitingList->customer_name} added to waiting list for {$waitingList->service->name}",
            'slot_available' => "Available slot found for {$waitingList->customer_name} waiting for {$waitingList->service->name}",
            'customer_notified' => "Customer {$waitingList->customer_name} has been notified about available slot",
            'converted_to_booking' => "Waiting list entry for {$waitingList->customer_name} converted to booking",
        ];

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'waiting_list',
            $titles[$type] ?? 'Waiting List Update',
            $messages[$type] ?? 'Waiting list status updated',
            [
                'waiting_list_id' => $waitingList->id,
                'customer_name' => $waitingList->customer_name,
                'customer_email' => $waitingList->customer_email,
                'customer_phone' => $waitingList->customer_phone,
                'service_name' => $waitingList->service->name ?? 'Unknown Service',
                'service_id' => $waitingList->service_id,
                'preferred_date' => $waitingList->preferred_date?->toDateString(),
                'status' => $waitingList->status,
            ],
            'normal',
            'waiting_list',
            $waitingList->id
        );
    }

    /**
     * Create customer message notification.
     */
    public function createCustomerMessageNotification($business, $message)
    {
        $customerName = $message['customer_name'] ?? 'Unknown Customer';
        $subject = $message['subject'] ?? 'No Subject';

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'customer_message',
            'New Customer Message',
            "New message from {$customerName}: {$subject}",
            $message,
            'normal',
            'message',
            $message['message_id'] ?? null
        );
    }

    /**
     * Create system notification.
     */
    public function createSystemNotification($business, $type, $data)
    {
        $titles = [
            'maintenance' => 'System Maintenance',
            'update' => 'System Update',
            'feature' => 'New Feature Available',
            'security' => 'Security Alert',
            'backup' => 'Backup Completed',
        ];

        $priorities = [
            'maintenance' => 'high',
            'update' => 'normal',
            'feature' => 'low',
            'security' => 'urgent',
            'backup' => 'low',
        ];

        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'system',
            $titles[$type] ?? 'System Notification',
            $data['message'] ?? 'System notification',
            $data,
            $priorities[$type] ?? 'normal',
            'system',
            null,
            $data['expires_at'] ?? null
        );
    }

    /**
     * Create marketing notification.
     */
    public function createMarketingNotification($business, $campaign)
    {
        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'marketing',
            $campaign['title'] ?? 'Marketing Opportunity',
            $campaign['message'] ?? 'New marketing opportunity available',
            $campaign,
            'low',
            'marketing',
            $campaign['campaign_id'] ?? null,
            $campaign['expires_at'] ?? null
        );
    }

    /**
     * Create reminder notification.
     */
    public function createReminderNotification($business, $reminder)
    {
        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'reminder',
            $reminder['title'] ?? 'Reminder',
            $reminder['message'] ?? 'You have a reminder',
            $reminder,
            $reminder['priority'] ?? 'normal',
            'reminder',
            $reminder['reminder_id'] ?? null,
            $reminder['expires_at'] ?? null
        );
    }

    /**
     * Create alert notification.
     */
    public function createAlertNotification($business, $alert)
    {
        return $this->createNotification(
            $business->id,
            $business->owner_id,
            'alert',
            $alert['title'] ?? 'Alert',
            $alert['message'] ?? 'System alert',
            $alert,
            $alert['priority'] ?? 'high',
            'alert',
            $alert['alert_id'] ?? null,
            $alert['expires_at'] ?? null
        );
    }

    /**
     * Cleanup expired notifications.
     */
    public function cleanupExpiredNotifications()
    {
        $count = OwnerNotification::whereNotNull('expires_at')
                                 ->where('expires_at', '<=', now())
                                 ->where('is_deleted', false)
                                 ->update(['is_deleted' => true, 'deleted_at' => now()]);

        Log::info("Cleaned up {$count} expired owner notifications");
        return $count;
    }

    /**
     * Get notification statistics for a business.
     */
    public function getNotificationStats($businessId, $ownerId)
    {
        $baseQuery = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId);

        return [
            'total' => (clone $baseQuery)->notDeleted()->count(),
            'unread' => (clone $baseQuery)->unread()->notDeleted()->count(),
            'urgent' => (clone $baseQuery)->urgent()->notDeleted()->count(),
            'today' => (clone $baseQuery)->whereDate('created_at', today())->notDeleted()->count(),
            'this_week' => (clone $baseQuery)->where('created_at', '>=', now()->startOfWeek())->notDeleted()->count(),
        ];
    }
}
