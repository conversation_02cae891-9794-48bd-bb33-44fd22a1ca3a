<?php

namespace App\Services;

use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class NotificationAIService
{
    /**
     * Analyze notification sentiment and priority
     */
    public function analyzeNotification(array $notificationData): array
    {
        try {
            $analysis = [
                'priority' => $this->calculatePriority($notificationData),
                'sentiment' => $this->analyzeSentiment($notificationData),
                'category' => $this->categorizeNotification($notificationData),
                'urgency_score' => $this->calculateUrgencyScore($notificationData),
                'suggested_actions' => $this->suggestActions($notificationData),
                'tags' => $this->generateTags($notificationData)
            ];

            return $analysis;
        } catch (\Exception $e) {
            Log::error('Failed to analyze notification with AI', [
                'notification_data' => $notificationData,
                'error' => $e->getMessage()
            ]);

            // Return default analysis on failure
            return [
                'priority' => $notificationData['priority'] ?? 'normal',
                'sentiment' => 'neutral',
                'category' => $notificationData['notification_type'] ?? 'general',
                'urgency_score' => 50,
                'suggested_actions' => [],
                'tags' => []
            ];
        }
    }

    /**
     * Calculate notification priority based on content and context
     */
    private function calculatePriority(array $data): string
    {
        $score = 0;
        $type = $data['notification_type'] ?? 'system';
        $message = strtolower($data['message'] ?? '');
        $title = strtolower($data['title'] ?? '');

        // Base priority by type
        $typePriorities = [
            'cancellation' => 30,
            'payment' => 25,
            'booking' => 20,
            'review' => 15,
            'customer_message' => 15,
            'waiting_list' => 10,
            'system' => 10,
            'marketing' => 5,
            'alert' => 35,
            'reminder' => 10
        ];

        $score += $typePriorities[$type] ?? 10;

        // Keyword analysis for urgency
        $urgentKeywords = [
            'urgent', 'emergency', 'asap', 'immediately', 'critical', 'failed',
            'error', 'problem', 'issue', 'complaint', 'refund', 'dispute',
            'cancelled', 'no-show', 'late', 'overdue'
        ];

        $highKeywords = [
            'important', 'priority', 'attention', 'review', 'feedback',
            'payment', 'booking', 'appointment', 'schedule'
        ];

        foreach ($urgentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false || strpos($title, $keyword) !== false) {
                $score += 20;
                break;
            }
        }

        foreach ($highKeywords as $keyword) {
            if (strpos($message, $keyword) !== false || strpos($title, $keyword) !== false) {
                $score += 10;
                break;
            }
        }

        // Amount-based priority (for payment notifications)
        if (isset($data['data']['amount'])) {
            $amount = floatval($data['data']['amount']);
            if ($amount > 500) $score += 15;
            elseif ($amount > 100) $score += 10;
            elseif ($amount > 50) $score += 5;
        }

        // Time-based priority (for bookings)
        if (isset($data['data']['appointment_date'])) {
            $appointmentTime = strtotime($data['data']['appointment_date']);
            $hoursUntil = ($appointmentTime - time()) / 3600;
            
            if ($hoursUntil < 2) $score += 25;
            elseif ($hoursUntil < 24) $score += 15;
            elseif ($hoursUntil < 72) $score += 10;
        }

        // Customer rating impact (for reviews)
        if (isset($data['data']['rating'])) {
            $rating = intval($data['data']['rating']);
            if ($rating <= 2) $score += 20;
            elseif ($rating == 3) $score += 10;
            elseif ($rating >= 4) $score += 5;
        }

        // Convert score to priority level
        if ($score >= 60) return 'urgent';
        elseif ($score >= 40) return 'high';
        elseif ($score >= 20) return 'normal';
        else return 'low';
    }

    /**
     * Analyze sentiment of notification content
     */
    private function analyzeSentiment(array $data): string
    {
        $message = strtolower($data['message'] ?? '');
        $title = strtolower($data['title'] ?? '');
        $content = $message . ' ' . $title;

        $positiveWords = [
            'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love',
            'perfect', 'awesome', 'outstanding', 'satisfied', 'happy', 'pleased',
            'recommend', 'best', 'good', 'thank', 'appreciate'
        ];

        $negativeWords = [
            'terrible', 'awful', 'horrible', 'worst', 'hate', 'disappointed',
            'unsatisfied', 'poor', 'bad', 'complaint', 'problem', 'issue',
            'failed', 'error', 'wrong', 'cancel', 'refund', 'dispute'
        ];

        $positiveCount = 0;
        $negativeCount = 0;

        foreach ($positiveWords as $word) {
            $positiveCount += substr_count($content, $word);
        }

        foreach ($negativeWords as $word) {
            $negativeCount += substr_count($content, $word);
        }

        if ($positiveCount > $negativeCount) return 'positive';
        elseif ($negativeCount > $positiveCount) return 'negative';
        else return 'neutral';
    }

    /**
     * Categorize notification into specific business categories
     */
    private function categorizeNotification(array $data): string
    {
        $type = $data['notification_type'] ?? 'general';
        $message = strtolower($data['message'] ?? '');
        $title = strtolower($data['title'] ?? '');

        // Enhanced categorization based on content analysis
        $categories = [
            'revenue' => ['payment', 'invoice', 'billing', 'charge', 'refund', 'money'],
            'customer_service' => ['complaint', 'feedback', 'support', 'help', 'question'],
            'operations' => ['booking', 'appointment', 'schedule', 'calendar', 'availability'],
            'marketing' => ['review', 'rating', 'testimonial', 'promotion', 'campaign'],
            'technical' => ['system', 'error', 'update', 'maintenance', 'bug'],
            'staff' => ['employee', 'staff', 'team', 'assignment', 'schedule'],
            'inventory' => ['stock', 'supply', 'equipment', 'resource', 'material']
        ];

        foreach ($categories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($message, $keyword) !== false || strpos($title, $keyword) !== false) {
                    return $category;
                }
            }
        }

        return $type;
    }

    /**
     * Calculate urgency score (0-100)
     */
    private function calculateUrgencyScore(array $data): int
    {
        $priority = $this->calculatePriority($data);
        $sentiment = $this->analyzeSentiment($data);

        $baseScores = [
            'urgent' => 85,
            'high' => 65,
            'normal' => 45,
            'low' => 25
        ];

        $score = $baseScores[$priority] ?? 45;

        // Adjust based on sentiment
        if ($sentiment === 'negative') $score += 15;
        elseif ($sentiment === 'positive') $score -= 5;

        // Ensure score is within bounds
        return max(0, min(100, $score));
    }

    /**
     * Suggest actions based on notification analysis
     */
    private function suggestActions(array $data): array
    {
        $actions = [];
        $type = $data['notification_type'] ?? 'system';
        $priority = $this->calculatePriority($data);
        $sentiment = $this->analyzeSentiment($data);

        switch ($type) {
            case 'booking':
                $actions[] = 'Send confirmation to customer';
                $actions[] = 'Update calendar';
                if ($priority === 'urgent') {
                    $actions[] = 'Call customer to confirm';
                }
                break;

            case 'cancellation':
                $actions[] = 'Process refund if applicable';
                $actions[] = 'Update availability';
                $actions[] = 'Notify waiting list customers';
                break;

            case 'payment':
                if ($sentiment === 'negative') {
                    $actions[] = 'Contact customer about payment issue';
                    $actions[] = 'Review payment method';
                } else {
                    $actions[] = 'Send payment confirmation';
                    $actions[] = 'Update financial records';
                }
                break;

            case 'review':
                if ($sentiment === 'negative') {
                    $actions[] = 'Respond to review professionally';
                    $actions[] = 'Contact customer to resolve issues';
                    $actions[] = 'Review service quality';
                } else {
                    $actions[] = 'Thank customer for positive review';
                    $actions[] = 'Share review on social media';
                }
                break;

            case 'customer_message':
                $actions[] = 'Respond to customer message';
                if ($priority === 'urgent') {
                    $actions[] = 'Prioritize immediate response';
                }
                break;
        }

        return $actions;
    }

    /**
     * Generate relevant tags for notification
     */
    private function generateTags(array $data): array
    {
        $tags = [];
        $type = $data['notification_type'] ?? 'general';
        $priority = $this->calculatePriority($data);
        $sentiment = $this->analyzeSentiment($data);

        // Add type-based tags
        $tags[] = $type;

        // Add priority tag
        if ($priority !== 'normal') {
            $tags[] = $priority . '_priority';
        }

        // Add sentiment tag
        if ($sentiment !== 'neutral') {
            $tags[] = $sentiment . '_sentiment';
        }

        // Add context-specific tags
        if (isset($data['data']['customer_name'])) {
            $tags[] = 'customer_related';
        }

        if (isset($data['data']['amount'])) {
            $amount = floatval($data['data']['amount']);
            if ($amount > 100) $tags[] = 'high_value';
        }

        if (isset($data['data']['appointment_date'])) {
            $hoursUntil = (strtotime($data['data']['appointment_date']) - time()) / 3600;
            if ($hoursUntil < 24) $tags[] = 'time_sensitive';
        }

        return array_unique($tags);
    }

    /**
     * Get notification insights for dashboard
     */
    public function getNotificationInsights($ownerId, $businessId): array
    {
        $cacheKey = "notification_insights_{$ownerId}_{$businessId}";
        
        return Cache::remember($cacheKey, 300, function () use ($ownerId, $businessId) {
            $notifications = OwnerNotification::where('owner_id', $ownerId)
                ->where('business_id', $businessId)
                ->where('created_at', '>=', now()->subDays(30))
                ->get();

            $insights = [
                'total_notifications' => $notifications->count(),
                'priority_distribution' => $notifications->groupBy('priority')->map->count(),
                'type_distribution' => $notifications->groupBy('notification_type')->map->count(),
                'response_time_avg' => $this->calculateAverageResponseTime($notifications),
                'sentiment_analysis' => $this->analyzeBulkSentiment($notifications),
                'trending_issues' => $this->identifyTrendingIssues($notifications),
                'recommendations' => $this->generateRecommendations($notifications)
            ];

            return $insights;
        });
    }

    /**
     * Calculate average response time for notifications
     */
    private function calculateAverageResponseTime($notifications): float
    {
        $responseTimes = $notifications->filter(function ($notification) {
            return $notification->read_at !== null;
        })->map(function ($notification) {
            return $notification->read_at->diffInMinutes($notification->created_at);
        });

        return $responseTimes->avg() ?? 0;
    }

    /**
     * Analyze sentiment across multiple notifications
     */
    private function analyzeBulkSentiment($notifications): array
    {
        $sentiments = $notifications->map(function ($notification) {
            return $this->analyzeSentiment($notification->toArray());
        });

        return [
            'positive' => $sentiments->filter(fn($s) => $s === 'positive')->count(),
            'neutral' => $sentiments->filter(fn($s) => $s === 'neutral')->count(),
            'negative' => $sentiments->filter(fn($s) => $s === 'negative')->count(),
        ];
    }

    /**
     * Identify trending issues from notifications
     */
    private function identifyTrendingIssues($notifications): array
    {
        // Simple keyword frequency analysis
        $keywords = [];
        
        foreach ($notifications as $notification) {
            $words = str_word_count(strtolower($notification->message . ' ' . $notification->title), 1);
            foreach ($words as $word) {
                if (strlen($word) > 4) { // Only consider words longer than 4 characters
                    $keywords[$word] = ($keywords[$word] ?? 0) + 1;
                }
            }
        }

        arsort($keywords);
        return array_slice($keywords, 0, 10, true);
    }

    /**
     * Generate recommendations based on notification patterns
     */
    private function generateRecommendations($notifications): array
    {
        $recommendations = [];

        // High cancellation rate
        $cancellations = $notifications->where('notification_type', 'cancellation')->count();
        $totalBookings = $notifications->where('notification_type', 'booking')->count();
        
        if ($totalBookings > 0 && ($cancellations / $totalBookings) > 0.2) {
            $recommendations[] = 'Consider implementing a cancellation policy or deposit system to reduce cancellation rate';
        }

        // Low review engagement
        $reviews = $notifications->where('notification_type', 'review')->count();
        if ($reviews < 5 && $notifications->count() > 20) {
            $recommendations[] = 'Encourage customers to leave reviews to improve online presence';
        }

        // Payment issues
        $paymentIssues = $notifications->where('notification_type', 'payment')
            ->filter(function ($n) {
                return $this->analyzeSentiment($n->toArray()) === 'negative';
            })->count();
            
        if ($paymentIssues > 3) {
            $recommendations[] = 'Review payment processing setup to reduce payment failures';
        }

        return $recommendations;
    }
}
