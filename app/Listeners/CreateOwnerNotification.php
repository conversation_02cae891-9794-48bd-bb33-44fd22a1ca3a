<?php

namespace App\Listeners;

use App\Services\OwnerNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateOwnerNotification implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(OwnerNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     */
    public function handle($event)
    {
        try {
            // Handle different types of events
            $eventClass = get_class($event);
            
            switch ($eventClass) {
                case 'App\Events\BookingCreated':
                    $this->handleBookingCreated($event);
                    break;
                    
                case 'App\Events\BookingCancelled':
                    $this->handleBookingCancelled($event);
                    break;
                    
                case 'App\Events\BookingModified':
                    $this->handleBookingModified($event);
                    break;
                    
                case 'App\Events\PaymentReceived':
                    $this->handlePaymentReceived($event);
                    break;
                    
                case 'App\Events\PaymentFailed':
                    $this->handlePaymentFailed($event);
                    break;
                    
                case 'App\Events\ReviewSubmitted':
                    $this->handleReviewSubmitted($event);
                    break;
                    
                case 'App\Events\WaitingListAdded':
                    $this->handleWaitingListAdded($event);
                    break;
                    
                case 'App\Events\CustomerMessage':
                    $this->handleCustomerMessage($event);
                    break;
                    
                default:
                    Log::info('Unhandled notification event', ['event' => $eventClass]);
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Failed to create owner notification', [
                'event' => get_class($event),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle booking created event.
     */
    protected function handleBookingCreated($event)
    {
        $booking = $event->booking;
        $this->notificationService->createBookingNotification($booking, 'booking');
    }

    /**
     * Handle booking cancelled event.
     */
    protected function handleBookingCancelled($event)
    {
        $booking = $event->booking;
        $this->notificationService->createBookingNotification($booking, 'cancellation');
    }

    /**
     * Handle booking modified event.
     */
    protected function handleBookingModified($event)
    {
        $booking = $event->booking;
        $this->notificationService->createBookingNotification($booking, 'modification');
    }

    /**
     * Handle payment received event.
     */
    protected function handlePaymentReceived($event)
    {
        $payment = $event->payment;
        $business = $payment->business ?? $payment->booking->business;
        
        $this->notificationService->createPaymentNotification(
            $business,
            'payment_received',
            [
                'payment_id' => $payment->id,
                'amount' => $payment->amount,
                'currency' => $payment->currency ?? 'USD',
                'customer_name' => $payment->customer_name ?? $payment->booking->customer_name,
                'booking_id' => $payment->booking_id ?? null,
                'transaction_id' => $payment->transaction_id ?? null,
            ]
        );
    }

    /**
     * Handle payment failed event.
     */
    protected function handlePaymentFailed($event)
    {
        $payment = $event->payment;
        $business = $payment->business ?? $payment->booking->business;
        
        $this->notificationService->createPaymentNotification(
            $business,
            'payment_failed',
            [
                'payment_id' => $payment->id,
                'amount' => $payment->amount,
                'currency' => $payment->currency ?? 'USD',
                'customer_name' => $payment->customer_name ?? $payment->booking->customer_name,
                'booking_id' => $payment->booking_id ?? null,
                'failure_reason' => $payment->failure_reason ?? 'Unknown',
            ]
        );
    }

    /**
     * Handle review submitted event.
     */
    protected function handleReviewSubmitted($event)
    {
        $review = $event->review;
        $business = $review->business;
        
        $this->notificationService->createReviewNotification(
            $business,
            [
                'review_id' => $review->id,
                'rating' => $review->rating,
                'customer_name' => $review->customer_name,
                'service_name' => $review->service->name ?? 'Unknown Service',
                'comment' => $review->comment ?? '',
                'booking_id' => $review->booking_id ?? null,
            ]
        );
    }

    /**
     * Handle waiting list added event.
     */
    protected function handleWaitingListAdded($event)
    {
        $waitingList = $event->waitingList;
        $this->notificationService->createWaitingListNotification($waitingList, 'new_entry');
    }

    /**
     * Handle customer message event.
     */
    protected function handleCustomerMessage($event)
    {
        $message = $event->message;
        $business = $message->business;
        
        $this->notificationService->createCustomerMessageNotification(
            $business,
            [
                'message_id' => $message->id,
                'customer_name' => $message->customer_name,
                'customer_email' => $message->customer_email,
                'subject' => $message->subject,
                'message' => $message->message,
                'type' => $message->type ?? 'general',
            ]
        );
    }

    /**
     * Handle the job failure.
     */
    public function failed($event, $exception)
    {
        Log::error('Owner notification listener failed', [
            'event' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
