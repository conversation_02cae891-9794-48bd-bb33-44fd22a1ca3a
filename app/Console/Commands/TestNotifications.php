<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OwnerNotification;
use App\Models\Business;
use App\Models\User;

class TestNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test notification system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing notification system...');

        // Test 1: Check if notifications exist
        $totalNotifications = OwnerNotification::count();
        $this->info("Total notifications in database: {$totalNotifications}");

        // Test 2: Check businesses and owners
        $businesses = Business::with('owner')->get();
        $this->info("Total businesses: {$businesses->count()}");

        foreach ($businesses as $business) {
            $notificationCount = OwnerNotification::where('business_id', $business->id)->count();
            $this->line("Business '{$business->name}' (ID: {$business->id}) has {$notificationCount} notifications");
            
            if ($business->owner) {
                $this->line("  Owner: {$business->owner->name} (ID: {$business->owner->id})");
            } else {
                $this->warn("  No owner found for business {$business->id}");
            }
        }

        // Test 3: Check notification types and statuses
        $unreadCount = OwnerNotification::where('is_read', false)->count();
        $readCount = OwnerNotification::where('is_read', true)->count();
        $deletedCount = OwnerNotification::where('is_deleted', true)->count();

        $this->info("Notification status breakdown:");
        $this->line("  Unread: {$unreadCount}");
        $this->line("  Read: {$readCount}");
        $this->line("  Deleted: {$deletedCount}");

        // Test 4: Check notification types
        $types = OwnerNotification::select('notification_type')
                                 ->selectRaw('count(*) as count')
                                 ->groupBy('notification_type')
                                 ->get();

        $this->info("Notification types:");
        foreach ($types as $type) {
            $this->line("  {$type->notification_type}: {$type->count}");
        }

        // Test 5: Test a specific business
        $firstBusiness = Business::first();
        if ($firstBusiness) {
            $this->info("Testing with first business: {$firstBusiness->name}");
            
            $businessNotifications = OwnerNotification::where('business_id', $firstBusiness->id)
                                                     ->where('owner_id', $firstBusiness->owner_id)
                                                     ->get();
            
            $this->line("Notifications for this business: {$businessNotifications->count()}");
            
            if ($businessNotifications->count() > 0) {
                $firstNotification = $businessNotifications->first();
                $this->line("First notification:");
                $this->line("  ID: {$firstNotification->id}");
                $this->line("  Title: {$firstNotification->title}");
                $this->line("  Type: {$firstNotification->notification_type}");
                $this->line("  Read: " . ($firstNotification->is_read ? 'Yes' : 'No'));
                $this->line("  Created: {$firstNotification->created_at}");
            }
        }

        $this->info('Notification system test completed!');
        return 0;
    }
}
