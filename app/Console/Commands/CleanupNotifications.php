<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OwnerNotification;
use Carbon\Carbon;

class CleanupNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:cleanup {--days=90} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old notifications and expired notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $force = $this->option('force');

        $this->info("Starting notification cleanup...");

        // Clean up expired notifications
        $expiredCount = $this->cleanupExpiredNotifications();
        
        // Clean up old deleted notifications
        $deletedCount = $this->cleanupOldDeletedNotifications($days);
        
        // Clean up very old read notifications (optional)
        $oldReadCount = 0;
        if ($force) {
            $oldReadCount = $this->cleanupOldReadNotifications($days);
        }

        $this->info("Cleanup completed:");
        $this->line("- Expired notifications: {$expiredCount}");
        $this->line("- Old deleted notifications: {$deletedCount}");
        if ($force) {
            $this->line("- Old read notifications: {$oldReadCount}");
        }

        $totalCleaned = $expiredCount + $deletedCount + $oldReadCount;
        $this->info("Total notifications cleaned: {$totalCleaned}");

        return 0;
    }

    /**
     * Clean up expired notifications.
     */
    protected function cleanupExpiredNotifications()
    {
        $count = OwnerNotification::whereNotNull('expires_at')
                                 ->where('expires_at', '<=', now())
                                 ->where('is_deleted', false)
                                 ->update([
                                     'is_deleted' => true,
                                     'deleted_at' => now()
                                 ]);

        $this->line("Marked {$count} expired notifications as deleted");
        return $count;
    }

    /**
     * Clean up old deleted notifications.
     */
    protected function cleanupOldDeletedNotifications($days)
    {
        $cutoffDate = Carbon::now()->subDays($days);
        
        $count = OwnerNotification::where('is_deleted', true)
                                 ->where('deleted_at', '<=', $cutoffDate)
                                 ->delete();

        $this->line("Permanently deleted {$count} old deleted notifications (older than {$days} days)");
        return $count;
    }

    /**
     * Clean up very old read notifications (only with --force flag).
     */
    protected function cleanupOldReadNotifications($days)
    {
        $cutoffDate = Carbon::now()->subDays($days * 2); // Double the days for read notifications
        
        $count = OwnerNotification::where('is_read', true)
                                 ->where('read_at', '<=', $cutoffDate)
                                 ->where('is_deleted', false)
                                 ->where('priority', '!=', 'urgent') // Keep urgent notifications longer
                                 ->update([
                                     'is_deleted' => true,
                                     'deleted_at' => now()
                                 ]);

        $this->line("Marked {$count} very old read notifications as deleted (older than " . ($days * 2) . " days)");
        return $count;
    }
}
