<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Business;
use App\Models\OwnerNotification;
use App\Services\OwnerNotificationService;
use Carbon\Carbon;

class GenerateSampleNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:generate-samples {business_id?} {--count=20}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sample notifications for testing purposes';

    protected $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(OwnerNotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $businessId = $this->argument('business_id');
        $count = $this->option('count');

        if ($businessId) {
            $business = Business::find($businessId);
            if (!$business) {
                $this->error("Business with ID {$businessId} not found.");
                return 1;
            }
            $businesses = collect([$business]);
        } else {
            $businesses = Business::with('owner')->active()->get();
        }

        if ($businesses->isEmpty()) {
            $this->error('No active businesses found.');
            return 1;
        }

        $this->info("Generating {$count} sample notifications for {$businesses->count()} business(es)...");

        $totalGenerated = 0;

        foreach ($businesses as $business) {
            $this->info("Generating notifications for business: {$business->name}");
            
            for ($i = 0; $i < $count; $i++) {
                $this->generateRandomNotification($business);
                $totalGenerated++;
            }
        }

        $this->info("Successfully generated {$totalGenerated} sample notifications!");
        return 0;
    }

    /**
     * Generate a random notification for a business.
     */
    protected function generateRandomNotification(Business $business)
    {
        $types = [
            'booking', 'cancellation', 'payment', 'review', 'system',
            'marketing', 'alert', 'reminder', 'customer_message', 'waiting_list'
        ];

        $priorities = ['low', 'normal', 'high', 'urgent'];
        
        $type = $types[array_rand($types)];
        $priority = $priorities[array_rand($priorities)];

        // Generate random dates within the last 30 days
        $createdAt = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

        $notificationData = $this->getNotificationData($type, $business);

        OwnerNotification::create([
            'business_id' => $business->id,
            'owner_id' => $business->owner_id,
            'notification_type' => $type,
            'title' => $notificationData['title'],
            'message' => $notificationData['message'],
            'data' => $notificationData['data'],
            'source_type' => $notificationData['source_type'],
            'source_id' => $notificationData['source_id'],
            'priority' => $priority,
            'is_read' => rand(0, 1) === 1, // Random read status
            'read_at' => rand(0, 1) === 1 ? $createdAt->addMinutes(rand(1, 60)) : null,
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ]);
    }

    /**
     * Get notification data based on type.
     */
    protected function getNotificationData($type, $business)
    {
        $customerNames = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown', 'Lisa Davis'];
        $serviceNames = ['Haircut', 'Massage', 'Consultation', 'Cleaning', 'Repair', 'Training'];
        
        $customerName = $customerNames[array_rand($customerNames)];
        $serviceName = $serviceNames[array_rand($serviceNames)];

        switch ($type) {
            case 'booking':
                return [
                    'title' => 'New Booking Received',
                    'message' => "New appointment booked by {$customerName} for {$serviceName}",
                    'data' => [
                        'customer_name' => $customerName,
                        'service_name' => $serviceName,
                        'appointment_date' => Carbon::now()->addDays(rand(1, 14))->toISOString(),
                        'booking_value' => rand(50, 500),
                    ],
                    'source_type' => 'booking',
                    'source_id' => rand(1, 1000),
                ];

            case 'cancellation':
                return [
                    'title' => 'Booking Cancelled',
                    'message' => "Appointment cancelled by {$customerName} for {$serviceName}",
                    'data' => [
                        'customer_name' => $customerName,
                        'service_name' => $serviceName,
                        'cancellation_reason' => 'Customer request',
                        'refund_amount' => rand(50, 500),
                    ],
                    'source_type' => 'booking',
                    'source_id' => rand(1, 1000),
                ];

            case 'payment':
                return [
                    'title' => 'Payment Received',
                    'message' => "Payment of $" . rand(50, 500) . " received from {$customerName}",
                    'data' => [
                        'customer_name' => $customerName,
                        'amount' => rand(50, 500),
                        'currency' => 'USD',
                        'transaction_id' => 'TXN' . rand(100000, 999999),
                    ],
                    'source_type' => 'payment',
                    'source_id' => rand(1, 1000),
                ];

            case 'review':
                $rating = rand(1, 5);
                return [
                    'title' => $rating >= 4 ? 'New Positive Review' : ($rating <= 2 ? 'New Negative Review' : 'New Review'),
                    'message' => "New {$rating}-star review from {$customerName} for {$serviceName}",
                    'data' => [
                        'customer_name' => $customerName,
                        'service_name' => $serviceName,
                        'rating' => $rating,
                        'comment' => 'Great service, highly recommended!',
                    ],
                    'source_type' => 'review',
                    'source_id' => rand(1, 1000),
                ];

            case 'system':
                $systemMessages = [
                    'System maintenance scheduled for tonight',
                    'New feature available: Advanced reporting',
                    'Security update completed successfully',
                    'Backup completed successfully',
                ];
                $message = $systemMessages[array_rand($systemMessages)];
                return [
                    'title' => 'System Notification',
                    'message' => $message,
                    'data' => ['type' => 'maintenance'],
                    'source_type' => 'system',
                    'source_id' => null,
                ];

            case 'marketing':
                return [
                    'title' => 'Marketing Opportunity',
                    'message' => 'New promotional campaign available for your business',
                    'data' => [
                        'campaign_name' => 'Summer Special',
                        'discount' => '20%',
                        'expires_at' => Carbon::now()->addDays(30)->toISOString(),
                    ],
                    'source_type' => 'marketing',
                    'source_id' => rand(1, 100),
                ];

            case 'alert':
                return [
                    'title' => 'Business Alert',
                    'message' => 'Multiple booking conflicts detected for tomorrow',
                    'data' => ['conflict_count' => rand(2, 5)],
                    'source_type' => 'alert',
                    'source_id' => null,
                ];

            case 'reminder':
                return [
                    'title' => 'Business Reminder',
                    'message' => 'Don\'t forget to update your business hours for the holiday',
                    'data' => ['holiday' => 'Christmas'],
                    'source_type' => 'reminder',
                    'source_id' => null,
                ];

            case 'customer_message':
                return [
                    'title' => 'New Customer Message',
                    'message' => "New message from {$customerName}: Question about services",
                    'data' => [
                        'customer_name' => $customerName,
                        'subject' => 'Question about services',
                        'message' => 'Hi, I would like to know more about your services.',
                    ],
                    'source_type' => 'message',
                    'source_id' => rand(1, 1000),
                ];

            case 'waiting_list':
                return [
                    'title' => 'New Waiting List Entry',
                    'message' => "New customer {$customerName} added to waiting list for {$serviceName}",
                    'data' => [
                        'customer_name' => $customerName,
                        'service_name' => $serviceName,
                        'preferred_date' => Carbon::now()->addDays(rand(1, 7))->toDateString(),
                    ],
                    'source_type' => 'waiting_list',
                    'source_id' => rand(1, 1000),
                ];

            default:
                return [
                    'title' => 'General Notification',
                    'message' => 'You have a new notification',
                    'data' => [],
                    'source_type' => null,
                    'source_id' => null,
                ];
        }
    }
}
