<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\OwnerNotificationService;
use Illuminate\Support\Facades\Log;

class ProcessOwnerNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notificationType;
    protected $notificationData;
    protected $businessId;
    protected $ownerId;

    /**
     * Create a new job instance.
     */
    public function __construct($notificationType, $notificationData, $businessId, $ownerId)
    {
        $this->notificationType = $notificationType;
        $this->notificationData = $notificationData;
        $this->businessId = $businessId;
        $this->ownerId = $ownerId;
    }

    /**
     * Execute the job.
     */
    public function handle(OwnerNotificationService $notificationService)
    {
        try {
            switch ($this->notificationType) {
                case 'booking_created':
                    $this->handleBookingCreated($notificationService);
                    break;
                    
                case 'booking_cancelled':
                    $this->handleBookingCancelled($notificationService);
                    break;
                    
                case 'booking_modified':
                    $this->handleBookingModified($notificationService);
                    break;
                    
                case 'payment_received':
                    $this->handlePaymentReceived($notificationService);
                    break;
                    
                case 'payment_failed':
                    $this->handlePaymentFailed($notificationService);
                    break;
                    
                case 'review_submitted':
                    $this->handleReviewSubmitted($notificationService);
                    break;
                    
                case 'waiting_list_added':
                    $this->handleWaitingListAdded($notificationService);
                    break;
                    
                case 'customer_message':
                    $this->handleCustomerMessage($notificationService);
                    break;
                    
                case 'system_alert':
                    $this->handleSystemAlert($notificationService);
                    break;
                    
                case 'marketing_campaign':
                    $this->handleMarketingCampaign($notificationService);
                    break;
                    
                default:
                    Log::warning('Unknown notification type', [
                        'type' => $this->notificationType,
                        'business_id' => $this->businessId
                    ]);
                    break;
            }
        } catch (\Exception $e) {
            Log::error('Failed to process owner notification', [
                'type' => $this->notificationType,
                'business_id' => $this->businessId,
                'owner_id' => $this->ownerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // Re-throw to trigger job retry
        }
    }

    /**
     * Handle booking created notification.
     */
    protected function handleBookingCreated($notificationService)
    {
        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'booking',
            'New Booking Received',
            "New appointment booked by {$this->notificationData['customer_name']} for {$this->notificationData['service_name']}",
            $this->notificationData,
            'normal',
            'booking',
            $this->notificationData['booking_id'] ?? null
        );
    }

    /**
     * Handle booking cancelled notification.
     */
    protected function handleBookingCancelled($notificationService)
    {
        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'cancellation',
            'Booking Cancelled',
            "Appointment cancelled by {$this->notificationData['customer_name']} for {$this->notificationData['service_name']}",
            $this->notificationData,
            'high',
            'booking',
            $this->notificationData['booking_id'] ?? null
        );
    }

    /**
     * Handle booking modified notification.
     */
    protected function handleBookingModified($notificationService)
    {
        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'booking',
            'Booking Modified',
            "Appointment modified by {$this->notificationData['customer_name']} for {$this->notificationData['service_name']}",
            $this->notificationData,
            'normal',
            'booking',
            $this->notificationData['booking_id'] ?? null
        );
    }

    /**
     * Handle payment received notification.
     */
    protected function handlePaymentReceived($notificationService)
    {
        $amount = $this->notificationData['amount'] ?? 0;
        $currency = $this->notificationData['currency'] ?? 'USD';
        $customerName = $this->notificationData['customer_name'] ?? 'Unknown Customer';

        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'payment',
            'Payment Received',
            "Payment of {$currency} {$amount} received from {$customerName}",
            $this->notificationData,
            'normal',
            'payment',
            $this->notificationData['payment_id'] ?? null
        );
    }

    /**
     * Handle payment failed notification.
     */
    protected function handlePaymentFailed($notificationService)
    {
        $amount = $this->notificationData['amount'] ?? 0;
        $currency = $this->notificationData['currency'] ?? 'USD';
        $customerName = $this->notificationData['customer_name'] ?? 'Unknown Customer';

        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'payment',
            'Payment Failed',
            "Payment of {$currency} {$amount} failed for {$customerName}",
            $this->notificationData,
            'high',
            'payment',
            $this->notificationData['payment_id'] ?? null
        );
    }

    /**
     * Handle review submitted notification.
     */
    protected function handleReviewSubmitted($notificationService)
    {
        $rating = $this->notificationData['rating'] ?? 0;
        $customerName = $this->notificationData['customer_name'] ?? 'Anonymous';
        $serviceName = $this->notificationData['service_name'] ?? 'Unknown Service';

        $title = $rating >= 4 ? 'New Positive Review' : ($rating <= 2 ? 'New Negative Review' : 'New Review');
        $priority = $rating <= 2 ? 'high' : 'normal';

        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'review',
            $title,
            "New {$rating}-star review from {$customerName} for {$serviceName}",
            $this->notificationData,
            $priority,
            'review',
            $this->notificationData['review_id'] ?? null
        );
    }

    /**
     * Handle waiting list added notification.
     */
    protected function handleWaitingListAdded($notificationService)
    {
        $customerName = $this->notificationData['customer_name'] ?? 'Unknown Customer';
        $serviceName = $this->notificationData['service_name'] ?? 'Unknown Service';

        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'waiting_list',
            'New Waiting List Entry',
            "New customer {$customerName} added to waiting list for {$serviceName}",
            $this->notificationData,
            'normal',
            'waiting_list',
            $this->notificationData['waiting_list_id'] ?? null
        );
    }

    /**
     * Handle customer message notification.
     */
    protected function handleCustomerMessage($notificationService)
    {
        $customerName = $this->notificationData['customer_name'] ?? 'Unknown Customer';
        $subject = $this->notificationData['subject'] ?? 'No Subject';

        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'customer_message',
            'New Customer Message',
            "New message from {$customerName}: {$subject}",
            $this->notificationData,
            'normal',
            'message',
            $this->notificationData['message_id'] ?? null
        );
    }

    /**
     * Handle system alert notification.
     */
    protected function handleSystemAlert($notificationService)
    {
        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'alert',
            $this->notificationData['title'] ?? 'System Alert',
            $this->notificationData['message'] ?? 'System alert notification',
            $this->notificationData,
            $this->notificationData['priority'] ?? 'high',
            'alert',
            null
        );
    }

    /**
     * Handle marketing campaign notification.
     */
    protected function handleMarketingCampaign($notificationService)
    {
        $notificationService->createNotification(
            $this->businessId,
            $this->ownerId,
            'marketing',
            $this->notificationData['title'] ?? 'Marketing Opportunity',
            $this->notificationData['message'] ?? 'New marketing opportunity available',
            $this->notificationData,
            'low',
            'marketing',
            $this->notificationData['campaign_id'] ?? null,
            $this->notificationData['expires_at'] ?? null
        );
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Owner notification job failed', [
            'type' => $this->notificationType,
            'business_id' => $this->businessId,
            'owner_id' => $this->ownerId,
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
