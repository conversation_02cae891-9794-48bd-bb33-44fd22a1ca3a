<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class OwnerNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'notification_type',
        'title',
        'message',
        'data',
        'source_type',
        'source_id',
        'is_read',
        'is_deleted',
        'priority',
        'expires_at',
        'read_at',
        'deleted_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'is_deleted' => 'boolean',
        'expires_at' => 'datetime',
        'read_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = [
        'expires_at',
        'read_at',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    // Global scope for enterprise isolation
    protected static function booted()
    {
        static::addGlobalScope('business_isolation', function (Builder $builder) {
            if (auth()->check() && auth()->user()->hasRole('owner')) {
                $business = auth()->user()->ownedBusinesses()->active()->first();
                if ($business) {
                    $builder->where('business_id', $business->id)
                           ->where('owner_id', auth()->id());
                }
            }
        });
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeNotDeleted($query)
    {
        return $query->where('is_deleted', false);
    }

    public function scopeDeleted($query)
    {
        return $query->where('is_deleted', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('notification_type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeUrgent($query)
    {
        return $query->where('priority', 'urgent');
    }

    public function scopeHigh($query)
    {
        return $query->where('priority', 'high');
    }

    public function scopeActive($query)
    {
        return $query->where('is_deleted', false)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    public function scopeExpired($query)
    {
        return $query->whereNotNull('expires_at')
                    ->where('expires_at', '<=', now());
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeForOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    // Helper methods
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    public function markAsUnread()
    {
        $this->update([
            'is_read' => false,
            'read_at' => null,
        ]);
    }

    public function softDelete()
    {
        $this->update([
            'is_deleted' => true,
            'deleted_at' => now(),
        ]);
    }

    public function restore()
    {
        $this->update([
            'is_deleted' => false,
            'deleted_at' => null,
        ]);
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isUrgent()
    {
        return $this->priority === 'urgent';
    }

    public function isHigh()
    {
        return $this->priority === 'high';
    }

    // Accessors
    public function getPriorityColorAttribute()
    {
        return match ($this->priority) {
            'urgent' => 'danger',
            'high' => 'warning',
            'normal' => 'info',
            'low' => 'secondary',
            default => 'info',
        };
    }

    public function getPriorityIconAttribute()
    {
        return match ($this->priority) {
            'urgent' => 'fas fa-exclamation-triangle',
            'high' => 'fas fa-exclamation-circle',
            'normal' => 'fas fa-info-circle',
            'low' => 'fas fa-minus-circle',
            default => 'fas fa-bell',
        };
    }

    public function getTypeIconAttribute()
    {
        return match ($this->notification_type) {
            'booking' => 'fas fa-calendar-check',
            'cancellation' => 'fas fa-calendar-times',
            'payment' => 'fas fa-credit-card',
            'review' => 'fas fa-star',
            'system' => 'fas fa-cog',
            'marketing' => 'fas fa-bullhorn',
            'alert' => 'fas fa-exclamation-triangle',
            'reminder' => 'fas fa-clock',
            'customer_message' => 'fas fa-comment',
            'waiting_list' => 'fas fa-list',
            default => 'fas fa-bell',
        };
    }

    public function getTypeColorAttribute()
    {
        return match ($this->notification_type) {
            'booking' => 'success',
            'cancellation' => 'danger',
            'payment' => 'info',
            'review' => 'warning',
            'system' => 'secondary',
            'marketing' => 'primary',
            'alert' => 'danger',
            'reminder' => 'warning',
            'customer_message' => 'info',
            'waiting_list' => 'primary',
            default => 'info',
        };
    }

    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function getShortMessageAttribute()
    {
        return strlen($this->message) > 100
            ? substr($this->message, 0, 100) . '...'
            : $this->message;
    }

    // Static methods for creating notifications
    public static function createForBusiness($businessId, $ownerId, $data)
    {
        return static::create(array_merge($data, [
            'business_id' => $businessId,
            'owner_id' => $ownerId,
        ]));
    }

    public static function createBookingNotification($booking, $type = 'booking')
    {
        $business = $booking->business;

        return static::createForBusiness($business->id, $business->owner_id, [
            'notification_type' => $type,
            'title' => static::getBookingNotificationTitle($booking, $type),
            'message' => static::getBookingNotificationMessage($booking, $type),
            'source_type' => 'booking',
            'source_id' => $booking->id,
            'priority' => $type === 'cancellation' ? 'high' : 'normal',
            'data' => [
                'booking_id' => $booking->id,
                'customer_name' => $booking->customer_name,
                'service_name' => $booking->service->name ?? 'Unknown Service',
                'appointment_date' => $booking->scheduled_at?->format('Y-m-d H:i'),
                'booking_value' => $booking->total_amount,
            ],
        ]);
    }

    private static function getBookingNotificationTitle($booking, $type)
    {
        return match ($type) {
            'booking' => 'New Booking Received',
            'cancellation' => 'Booking Cancelled',
            'modification' => 'Booking Modified',
            default => 'Booking Update',
        };
    }

    private static function getBookingNotificationMessage($booking, $type)
    {
        $customerName = $booking->customer_name;
        $serviceName = $booking->service->name ?? 'Unknown Service';
        $date = $booking->scheduled_at?->format('M j, Y \a\t g:i A');

        return match ($type) {
            'booking' => "New appointment booked by {$customerName} for {$serviceName} on {$date}",
            'cancellation' => "Appointment cancelled by {$customerName} for {$serviceName} scheduled on {$date}",
            'modification' => "Appointment modified by {$customerName} for {$serviceName}",
            default => "Booking update for {$customerName}",
        };
    }
}
