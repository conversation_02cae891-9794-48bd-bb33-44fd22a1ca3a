@extends('owner.layouts.app')

@section('title', 'Notification Details')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notification Details</h1>
            <p class="text-muted">View notification information</p>
        </div>
        <div>
            <a href="{{ route('owner.notifications.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Notifications
            </a>
        </div>
    </div>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }} fa-2x mr-3"></i>
                    <div>
                        <h3 class="card-title mb-0">{{ $notification->title }}</h3>
                        <small class="text-muted">
                            <i class="fas fa-clock mr-1"></i>
                            {{ $notification->created_at->format('M j, Y \a\t g:i A') }}
                            @if($notification->priority !== 'normal')
                                • <span class="badge badge-{{ $notification->priority_color }}">{{ strtoupper($notification->priority) }}</span>
                            @endif
                        </small>
                    </div>
                </div>
                <div>
                    @if(!$notification->is_read)
                        <button type="button" class="btn btn-success" id="markAsReadBtn" data-id="{{ $notification->id }}">
                            <i class="fas fa-check"></i> Mark as Read
                        </button>
                    @else
                        <span class="badge badge-success">
                            <i class="fas fa-check"></i> Read
                        </span>
                    @endif
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="notification-message mb-4">
                <h5>Message</h5>
                <p class="lead">{{ $notification->message }}</p>
            </div>

            @if($notification->data && count($notification->data) > 0)
                <div class="notification-details">
                    <h5>Additional Details</h5>
                    <div class="bg-light p-3 rounded">
                        <div class="row">
                            @foreach($notification->data as $key => $value)
                                @if($value !== null && $value !== '')
                                    <div class="col-md-6 mb-2">
                                        <strong>{{ ucwords(str_replace('_', ' ', $key)) }}:</strong>
                                        <span class="ml-2">
                                            @if(is_array($value))
                                                {{ implode(', ', $value) }}
                                            @elseif(str_contains($key, 'date') || str_contains($key, 'time'))
                                                {{ \Carbon\Carbon::parse($value)->format('M j, Y \a\t g:i A') }}
                                            @elseif(str_contains($key, 'amount') || str_contains($key, 'value'))
                                                ${{ number_format($value, 2) }}
                                            @else
                                                {{ $value }}
                                            @endif
                                        </span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            @if($notification->source_type && $notification->source_id)
                <div class="notification-source mt-4">
                    <h5>Source Information</h5>
                    <div class="bg-info p-3 rounded text-white">
                        <i class="fas fa-info-circle mr-2"></i>
                        This notification was generated from a <strong>{{ str_replace('_', ' ', $notification->source_type) }}</strong> 
                        with ID: <strong>{{ $notification->source_id }}</strong>
                    </div>
                </div>
            @endif
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    <small>
                        Created: {{ $notification->created_at->format('M j, Y \a\t g:i A') }}
                        @if($notification->read_at)
                            • Read: {{ $notification->read_at->format('M j, Y \a\t g:i A') }}
                        @endif
                    </small>
                </div>
                <div>
                    @if(!$notification->is_read)
                        <button type="button" class="btn btn-outline-success btn-sm mark-read" data-id="{{ $notification->id }}">
                            <i class="fas fa-check"></i> Mark as Read
                        </button>
                    @else
                        <button type="button" class="btn btn-outline-warning btn-sm mark-unread" data-id="{{ $notification->id }}">
                            <i class="fas fa-undo"></i> Mark as Unread
                        </button>
                    @endif
                    <button type="button" class="btn btn-outline-danger btn-sm delete-notification" data-id="{{ $notification->id }}">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Mark as read
    $('.mark-read, #markAsReadBtn').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsRead(notificationId);
    });

    // Mark as unread
    $('.mark-unread').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsUnread(notificationId);
    });

    // Delete notification
    $('.delete-notification').click(function() {
        const notificationId = $(this).data('id');
        if (confirm('Are you sure you want to delete this notification?')) {
            deleteNotification(notificationId);
        }
    });

    function markNotificationAsRead(notificationId) {
        $.post(`{{ route("owner.notifications.mark-read", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error('Failed to mark notification as read');
                }
            })
            .fail(function() {
                toastr.error('Failed to mark notification as read');
            });
    }

    function markNotificationAsUnread(notificationId) {
        $.post(`{{ route("owner.notifications.mark-unread", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error('Failed to mark notification as unread');
                }
            })
            .fail(function() {
                toastr.error('Failed to mark notification as unread');
            });
    }

    function deleteNotification(notificationId) {
        $.post(`{{ route("owner.notifications.delete", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    window.location.href = '{{ route("owner.notifications.index") }}';
                } else {
                    toastr.error('Failed to delete notification');
                }
            })
            .fail(function() {
                toastr.error('Failed to delete notification');
            });
    }
});
</script>
@stop
